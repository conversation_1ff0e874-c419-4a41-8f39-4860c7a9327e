import sys
import os
#测试加载文件包本文件要放到app与main.py同级别
# 加入 xqwerty 所在目录
sys.path.insert(0, os.path.abspath('./packages/aioewfde'))

try:
    import xqwerty
    print("成功导入 xqwerty 模块")
    print("模块内容:", dir(xqwerty))

    # 打印支持的模型（如有）
    if hasattr(xqwerty, "supported_models"):
        print("支持的模型:", xqwerty.supported_models)
    else:
        print("xqwerty 模块中无 supported_models 属性")
except Exception as e:
    print("导入 xqwerty 时出错:", e)


