# 使用阿里云Mac基础镜像（适配M4芯片）
FROM crpi-i4vze864zm9axbyn.cn-beijing.personal.cr.aliyuncs.com/bkonline/python:3.9-slim-maclocal

# 设置时区
ENV TZ=Asia/Shanghai
WORKDIR /app

# 添加 Aliyun apt 源到独立文件中，而不是覆盖原有 sources.list
RUN echo "deb http://mirrors.aliyun.com/debian stable main contrib non-free" > /etc/apt/sources.list.d/aliyun.list \
 && echo "deb http://mirrors.aliyun.com/debian stable-updates main contrib non-free" >> /etc/apt/sources.list.d/aliyun.list \
 && echo "deb http://mirrors.aliyun.com/debian-security stable-security main contrib non-free" >> /etc/apt/sources.list.d/aliyun.list \
 && apt-get update \
 && apt-get install -y --no-install-recommends \
        curl \
        vim \
        gcc \
        g++ \
        build-essential \
        libgl1-mesa-glx \
        libxext6 \
        libxrender1 \
        libxi6 \
        libglu1-mesa \
        libx11-6 \
        libglib2.0-0 \
        libglib2.0-dev \
        libgtk2.0-0 \
        libgtk2.0-dev \
        libgstreamer1.0-0 \
        libgstreamer1.0-dev \
        procps \
 && rm -rf /var/lib/apt/lists/*

 # 安装 Python 依赖 (包括Cython)
COPY ./requirements.txt /app/requirements.txt
ENV PIP_CACHE_DIR=/tmp/pipcache
RUN pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple && \
    pip install cython setuptools wheel -i https://mirrors.aliyun.com/pypi/simple && \
    rm -rf /tmp/pipcache

# 复制源代码和编译配置
COPY ./app /app/app
COPY ./setup.py /app/setup.py

# 执行 Cython 编译并清理
RUN python setup.py build_ext --inplace && \
    # 只删除已成功编译的.py文件
    find app -name "*.py" ! -name "__init__.py" ! -name "main.py" -exec sh -c 'for f; do if [ -f "${f%%.py}".*.so ]; then rm "$f"; fi; done' _ {} + && \
    # 清理编译产物
    find app -name "*.c" -delete && \
    rm -rf build/ && \
    find app -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true && \
    # 验证关键文件存在（不触发应用启动）
    ls -la app/main.py app/__init__.py && \
    # 验证编译结果
    find app -name "*.so" | wc -l

# 创建日志和临时上传目录
RUN mkdir -p /app/logs /app/temp_uploads && chmod 777 /app/logs /app/temp_uploads

# 创建启动脚本
RUN echo '#!/bin/bash\n\
export PYTHONPATH=/app:$PYTHONPATH\n\
cd /app\n\
exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 1 --limit-concurrency 10' > /app/start.sh && \
chmod +x /app/start.sh

EXPOSE 8000

CMD ["/app/start.sh"]
