from fastapi import APIRouter
from app.api.v1 import user, health, home, openai

# 创建API路由器
api_router = APIRouter()

# 用户相关路由
api_router.include_router(user.router, prefix="/users", tags=["users"])

# 健康相关路由  
api_router.include_router(health.router, prefix="/health", tags=["health"])

# 首页相关路由
api_router.include_router(home.router, prefix="/home", tags=["home"])

# AI分析相关路由
api_router.include_router(openai.router, prefix="/ai", tags=["ai-analysis"])
