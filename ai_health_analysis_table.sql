-- AI健康分析结果表
CREATE TABLE `ai_health_analysis` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `report_id` varchar(36) NOT NULL COMMENT '关联的健康报告ID',
  `constitution_type` varchar(50) DEFAULT NULL COMMENT '中医体质类型',
  `qi_blood_status` text COMMENT '气血状况描述',
  `organ_function` text COMMENT '脏腑功能分析',
  `health_alerts` text COMMENT '健康风险提示',
  `lifestyle_suggestions` json COMMENT '生活建议(饮食、作息、运动、情志)',
  `overall_advice` varchar(500) DEFAULT NULL COMMENT '总体建议一句话',
  `medical_consultation` enum('是','否') DEFAULT '否' COMMENT '是否需要就医',
  `ai_analysis_raw` json COMMENT 'AI返回的原始完整数据',
  `ai_model_used` varchar(50) DEFAULT 'qwen-plus' COMMENT '使用的AI模型',
  `analysis_version` varchar(20) DEFAULT 'v1.0' COMMENT '分析版本号',
  `tokens_used` int(11) DEFAULT 0 COMMENT '本次分析消耗的tokens',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_report_id` (`report_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI健康分析结果表';