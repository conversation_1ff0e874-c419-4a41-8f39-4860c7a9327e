from fastapi import APIRouter, HTTPException, Request, Depends
from fastapi.responses import JSONResponse
import os
import json
import datetime
import traceback
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.core.database import get_db_session, close_db_session
from app.models.health_report import HealthReport
from app.models.ai_health_analysis import AiHealthAnalysis
from app.core.auth import extract_token_from_request, get_current_user
from app.core.config import settings
from app.core.log_config import get_logger

# OpenAI兼容的客户端，用于调用阿里云百炼
from openai import OpenAI

# 日志配置
logger = get_logger('ai_analysis', 'logs/ai_analysis.log')

router = APIRouter(tags=["ai-analysis"])

# 初始化阿里云百炼客户端
client = OpenAI(
    api_key=settings.DASHSCOPE_API_KEY,
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

def extract_health_data_for_ai(health_report: HealthReport) -> Dict[str, Any]:
    """
    提取和格式化健康数据用于AI分析
    按照之前设计的数据简化策略
    """
    # 计算年龄
    age = None
    if health_report.age:
        age = health_report.age
    
    # 格式化血压数据
    blood_pressure_str = "未知"
    if health_report.blood_pressure:
        if isinstance(health_report.blood_pressure, dict):
            systolic = health_report.blood_pressure.get('systolic', 0)
            diastolic = health_report.blood_pressure.get('diastolic', 0)
            if systolic and diastolic:
                blood_pressure_str = f"{systolic}/{diastolic}"
        elif isinstance(health_report.blood_pressure, str):
            blood_pressure_str = health_report.blood_pressure
    
    # 简化HRV数据为评级
    autonomic_balance = "未知"
    if health_report.hrv_time_domain or health_report.hrv_frequency_domain:
        # 这里可以根据HRV的具体数值范围来判断
        # 简化为"平衡/交感亢进/副交感亢进"
        autonomic_balance = "平衡"  # 默认值，可根据实际HRV数据优化
    
    # 构建AI分析用的数据结构
    ai_data = {
        "patient_info": {
            "gender": health_report.gender or "未知",
            "age": age or "未知",
            "height": health_report.height or "未知", 
            "weight": health_report.weight or "未知",
            "bmi": health_report.bmi or "未知"
        },
        "vital_signs": {
            "heart_rate": health_report.heart_rate or "未知",
            "blood_pressure": blood_pressure_str,
            "breathing_rate": health_report.breathing_rate or "未知",
            "spo2": health_report.spo2 or "未知"
        },
        "health_indicators": {
            "hemoglobin": health_report.hemoglobin or "未知",
            "anemia": health_report.anemia or "未知",
            "cardiac_risk": health_report.cardiac_risk or "未知",
            "brain_risk": health_report.brain_risk or "未知"
        },
        "hrv_summary": {
            "autonomic_balance": autonomic_balance
        }
    }
    
    return ai_data

def build_ai_prompt(health_data: Dict[str, Any]) -> str:
    """
    构建AI分析的完整prompt
    """
    prompt = f"""# 角色设定
你是一位有50年临床经验的老中医，精通现代医学指标的中医解读，擅长通过生理数据进行体质辨识和养生指导。

## 分析任务
请根据以下健康数据进行中医分析，要求语言通俗易懂，面向普通用户：

### 输入数据
【基本信息】
年龄：{health_data['patient_info']['age']}岁，性别：{health_data['patient_info']['gender']}，身高：{health_data['patient_info']['height']}cm，体重：{health_data['patient_info']['weight']}kg，BMI：{health_data['patient_info']['bmi']}

【生命体征】
心率：{health_data['vital_signs']['heart_rate']}次/分，血压：{health_data['vital_signs']['blood_pressure']}mmHg
呼吸：{health_data['vital_signs']['breathing_rate']}次/分，血氧：{health_data['vital_signs']['spo2']}%

【健康指标】
血红蛋白：{health_data['health_indicators']['hemoglobin']}g/L，贫血状况：{health_data['health_indicators']['anemia']}
心脏风险：{health_data['health_indicators']['cardiac_risk']}，脑血管风险：{health_data['health_indicators']['brain_risk']}
自主神经状态：{health_data['hrv_summary']['autonomic_balance']}

### 分析要求
请从以下5个维度进行分析，每个维度不少于100字（饮食建议、作息建议、运动建议、情志调养每项也不能少于100字）：

1. **体质判断**：根据心率、血压、血氧、血红蛋白等数据，判断主要体质类型（如气虚、血瘀、痰湿、阴虚、阳虚等），并详细说明判断依据
2. **气血状况**：基于血红蛋白、血氧饱和度、心率等指标，详细评估气血盛衰和运行情况，分析气血是否充足、运行是否通畅
3. **脏腑功能**：根据心率、血压、呼吸频率、血氧等数据，深入分析心、肺、脾、肝、肾五脏功能状态。不要说"未明确检测"，要基于现有数据进行推导分析
4. **健康提示**：结合BMI、各项生理指标，详细指出需要注意的健康风险，包括潜在疾病风险和预防要点
5. **养生建议**：提供详细的饮食、作息、运动、情志调养建议，每项建议都要具体实用

### 输出格式要求
请严格按照以下JSON格式返回结果，确保是标准的JSON格式：

{{
  "constitution_type": "主要体质类型",
  "qi_blood_status": "气血状况描述",
  "organ_function": "脏腑功能分析",
  "health_alerts": "健康风险提示",
  "lifestyle_suggestions": {{
    "diet": "饮食建议",
    "sleep": "作息建议",
    "exercise": "运动建议", 
    "emotion": "情志调养"
  }},
  "overall_advice": "综合健康建议总结（150字左右）",
  "medical_consultation": "是否需要就医建议（是/否）"
}}

## 注意事项
- 避免使用专业医学术语，用通俗语言表达
- 当发现异常指标时，建议用户就医
- 所有建议仅供参考，不可替代专业医疗诊断"""

    return prompt

def process_ai_analysis_from_storage(ai_analysis: AiHealthAnalysis) -> Dict[str, Any]:
    """
    处理从数据库获取的AI分析结果
    将存储格式转换回标准格式
    """
    # 处理 health_alerts - 如果是换行分隔的字符串，转换回数组
    health_alerts = ai_analysis.health_alerts
    if isinstance(health_alerts, str) and health_alerts:
        health_alerts = health_alerts.split('\n') if '\n' in health_alerts else [health_alerts]
    elif not health_alerts:
        health_alerts = []
    
    return {
        "constitution_type": ai_analysis.constitution_type,
        "qi_blood_status": ai_analysis.qi_blood_status,
        "organ_function": ai_analysis.organ_function,
        "health_alerts": health_alerts,
        "lifestyle_suggestions": ai_analysis.lifestyle_suggestions,
        "overall_advice": ai_analysis.overall_advice,
        "medical_consultation": ai_analysis.medical_consultation
    }

def process_ai_analysis_for_storage(ai_analysis: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理AI分析结果以便存储到数据库
    将数组类型的字段转换为字符串，确保数据库兼容性
    """
    processed = ai_analysis.copy()
    
    # 处理 health_alerts - 如果是数组，转换为换行分隔的字符串
    health_alerts = processed.get('health_alerts')
    if isinstance(health_alerts, list):
        processed['health_alerts'] = '\n'.join(health_alerts)
    elif health_alerts is None:
        processed['health_alerts'] = ''
    
    # 处理 lifestyle_suggestions - 确保是字典格式
    lifestyle_suggestions = processed.get('lifestyle_suggestions')
    if isinstance(lifestyle_suggestions, str):
        try:
            processed['lifestyle_suggestions'] = json.loads(lifestyle_suggestions)
        except json.JSONDecodeError:
            # 如果解析失败，保持原字符串
            pass
    
    # 确保其他字段不为None
    for field in ['constitution_type', 'qi_blood_status', 'organ_function', 'overall_advice']:
        if processed.get(field) is None:
            processed[field] = ''
    
    # 处理 medical_consultation，确保只返回 '是' 或 '否'
    medical_consultation = processed.get('medical_consultation', '否')
    if medical_consultation not in ['是', '否']:
        processed['medical_consultation'] = '否'
    
    return processed

async def call_bailian_ai_analysis(health_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    调用阿里云百炼进行AI健康分析
    """
    try:
        prompt = build_ai_prompt(health_data)
        
        logger.info("开始调用阿里云百炼AI分析")
        
        response = client.chat.completions.create(
            model="qwen-plus",  # 使用 qwen-plus 模型
            messages=[
                {"role": "user", "content": prompt}
            ],
            response_format={"type": "json_object"},  # 确保返回JSON格式
            temperature=0.7,  # 适度的创造性
            max_tokens=2000   # 控制输出长度
        )
        
        # 获取返回结果
        ai_result = response.choices[0].message.content
        tokens_used = response.usage.total_tokens
        
        logger.info(f"AI分析完成，消耗tokens: {tokens_used}")
        
        # 解析JSON结果
        try:
            analysis_result = json.loads(ai_result)
            analysis_result['_meta'] = {
                'tokens_used': tokens_used,
                'model_used': 'qwen-turbo',
                'analysis_time': datetime.datetime.now().isoformat()
            }
            return analysis_result
        except json.JSONDecodeError as e:
            logger.error(f"AI返回结果JSON解析失败: {e}, 原始结果: {ai_result}")
            raise HTTPException(status_code=500, detail="AI分析结果格式错误")
            
    except Exception as e:
        logger.error(f"调用AI分析失败: {str(e)}")
        logger.error(f"详细错误: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"AI分析服务异常: {str(e)}")

@router.post("/health-analysis/{report_id}")
async def create_ai_health_analysis(report_id: str, request: Request):
    """
    基于健康报告生成AI中医分析
    
    Args:
        report_id: 健康报告ID
        
    Returns:
        AI分析结果
    """
    # 验证token并获取当前用户信息
    token = extract_token_from_request(request)
    current_user = get_current_user(token)
    current_user_id = int(current_user.get("sub", 0))
    
    db = None
    try:
        logger.info(f"开始AI健康分析，report_id: {report_id}, user_id: {current_user_id}")
        
        db = get_db_session()
        
        # 1. 验证健康报告是否存在且属于当前用户
        health_report = db.query(HealthReport).filter(
            HealthReport.report_id == report_id,
            HealthReport.uid == current_user_id
        ).first()
        
        if not health_report:
            raise HTTPException(status_code=404, detail="健康报告不存在或无权访问")
        
        logger.info(f"找到健康报告，姓名: {health_report.name}")
        
        # 2. 提取和格式化健康数据
        health_data = extract_health_data_for_ai(health_report)
        logger.info("健康数据提取完成")
        
        # 3. 调用AI分析
        ai_analysis = await call_bailian_ai_analysis(health_data)
        
        # 4. 处理AI分析结果以便存储
        processed_analysis = process_ai_analysis_for_storage(ai_analysis)
        
        # 5. 检查是否已存在分析结果
        existing_analysis = db.query(AiHealthAnalysis).filter(
            AiHealthAnalysis.report_id == report_id
        ).first()
        
        if existing_analysis:
            # 更新现有记录
            existing_analysis.constitution_type = processed_analysis.get('constitution_type')
            existing_analysis.qi_blood_status = processed_analysis.get('qi_blood_status')
            existing_analysis.organ_function = processed_analysis.get('organ_function')
            existing_analysis.health_alerts = processed_analysis.get('health_alerts')
            existing_analysis.lifestyle_suggestions = processed_analysis.get('lifestyle_suggestions')
            existing_analysis.overall_advice = processed_analysis.get('overall_advice')
            existing_analysis.medical_consultation = processed_analysis.get('medical_consultation', '否')
            existing_analysis.ai_analysis_raw = ai_analysis  # 保存原始数据
            existing_analysis.tokens_used = ai_analysis.get('_meta', {}).get('tokens_used', 0)
            existing_analysis.update_time = datetime.datetime.now()
            
            logger.info(f"更新现有AI分析记录，ID: {existing_analysis.id}")
        else:
            # 创建新记录
            new_analysis = AiHealthAnalysis(
                report_id=report_id,
                constitution_type=processed_analysis.get('constitution_type'),
                qi_blood_status=processed_analysis.get('qi_blood_status'),
                organ_function=processed_analysis.get('organ_function'),
                health_alerts=processed_analysis.get('health_alerts'),
                lifestyle_suggestions=processed_analysis.get('lifestyle_suggestions'),
                overall_advice=processed_analysis.get('overall_advice'),
                medical_consultation=processed_analysis.get('medical_consultation', '否'),
                ai_analysis_raw=ai_analysis,  # 保存原始数据
                ai_model_used='qwen-plus',
                analysis_version='v1.0',
                tokens_used=ai_analysis.get('_meta', {}).get('tokens_used', 0)
            )
            
            db.add(new_analysis)
            logger.info("创建新的AI分析记录")
        
        db.commit()
        logger.info("AI分析结果保存成功")
        
        # 6. 构建返回结果（使用原始AI分析结果，保持一致性）
        response_data = {
            "report_id": report_id,
            "patient_name": health_report.name,
            "analysis_result": {
                "constitution_type": ai_analysis.get('constitution_type'),
                "qi_blood_status": ai_analysis.get('qi_blood_status'),
                "organ_function": ai_analysis.get('organ_function'),
                "health_alerts": ai_analysis.get('health_alerts'),  # 保持原始格式
                "lifestyle_suggestions": ai_analysis.get('lifestyle_suggestions'),
                "overall_advice": ai_analysis.get('overall_advice'),
                "medical_consultation": ai_analysis.get('medical_consultation')
            },
            "analysis_meta": {
                "tokens_used": ai_analysis.get('_meta', {}).get('tokens_used', 0),
                "model_used": "qwen-plus",
                "analysis_time": datetime.datetime.now().isoformat(),
                "version": "v1.0"
            }
        }
        
        return JSONResponse(content=response_data)
        
    except HTTPException:
        # 重新抛出 HTTP 异常
        raise
    except Exception as e:
        logger.error(f"AI健康分析失败: {str(e)}")
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        if db:
            try:
                db.rollback()
            except Exception as rollback_error:
                logger.error(f"数据库回滚失败: {rollback_error}")
        raise HTTPException(status_code=500, detail=f"AI健康分析失败: {str(e)}")
    finally:
        if db:
            try:
                close_db_session(db)
            except Exception as close_error:
                logger.error(f"关闭数据库连接失败: {close_error}")

@router.get("/health-analysis/{report_id}")
async def get_ai_health_analysis(report_id: str, request: Request):
    """
    获取已存在的AI健康分析结果
    
    Args:
        report_id: 健康报告ID
        
    Returns:
        AI分析结果
    """
    # 验证token并获取当前用户信息
    token = extract_token_from_request(request)
    current_user = get_current_user(token)
    current_user_id = int(current_user.get("sub", 0))
    
    db = None
    try:
        db = get_db_session()
        
        # 1. 验证健康报告是否属于当前用户
        health_report = db.query(HealthReport).filter(
            HealthReport.report_id == report_id,
            HealthReport.uid == current_user_id
        ).first()
        
        if not health_report:
            raise HTTPException(status_code=404, detail="健康报告不存在或无权访问")
        
        # 2. 获取AI分析结果
        ai_analysis = db.query(AiHealthAnalysis).filter(
            AiHealthAnalysis.report_id == report_id
        ).first()
        
        if not ai_analysis:
            raise HTTPException(status_code=404, detail="AI分析结果不存在，请先进行分析")
        
        # 3. 处理数据库中的分析结果
        processed_result = process_ai_analysis_from_storage(ai_analysis)
        
        # 4. 构建返回结果
        response_data = {
            "report_id": report_id,
            "patient_name": health_report.name,
            "analysis_result": processed_result,
            "analysis_meta": {
                "tokens_used": ai_analysis.tokens_used,
                "model_used": ai_analysis.ai_model_used,
                "analysis_time": ai_analysis.create_time.isoformat(),
                "update_time": ai_analysis.update_time.isoformat(),
                "version": ai_analysis.analysis_version
            }
        }
        
        return JSONResponse(content=response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取AI健康分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取AI健康分析失败: {str(e)}")
    finally:
        if db:
            try:
                close_db_session(db)
            except Exception as close_error:
                logger.error(f"关闭数据库连接失败: {close_error}")

@router.get("/hello")
def ai_health_check():
    """AI分析模块健康检查"""
    return JSONResponse(content={
        "message": "AI Health Analysis API is running", 
        "status": "healthy",
        "dashscope_configured": bool(settings.DASHSCOPE_API_KEY)
    })