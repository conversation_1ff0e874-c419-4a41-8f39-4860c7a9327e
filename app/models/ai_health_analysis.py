from sqlalchemy import Column, Integer, String, DateTime, JSON, Text, Enum, BigInteger
from sqlalchemy.sql import func
from app.core.database import Base

class AiHealthAnalysis(Base):
    __tablename__ = "ai_health_analysis"
    
    id = Column(BigInteger, primary_key=True, index=True, autoincrement=True, comment='主键')
    report_id = Column(String(36), unique=True, nullable=False, index=True, comment='关联的健康报告ID')
    constitution_type = Column(String(50), comment='中医体质类型')
    qi_blood_status = Column(Text, comment='气血状况描述')
    organ_function = Column(Text, comment='脏腑功能分析')
    health_alerts = Column(Text, comment='健康风险提示')
    lifestyle_suggestions = Column(JSON, comment='生活建议(饮食、作息、运动、情志)')
    overall_advice = Column(String(500), comment='总体建议一句话')
    medical_consultation = Column(Enum('是', '否'), default='否', comment='是否需要就医')
    ai_analysis_raw = Column(JSON, comment='AI返回的原始完整数据')
    ai_model_used = Column(String(50), default='qwen-plus', comment='使用的AI模型')
    analysis_version = Column(String(20), default='v1.0', comment='分析版本号')
    tokens_used = Column(Integer, default=0, comment='本次分析消耗的tokens')
    create_time = Column(DateTime, server_default=func.now(), nullable=False, comment='创建时间')
    update_time = Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')