# Python缓存
__pycache__/
*.pyc
*.pyo
*.pyd

# 虚拟环境
.venv/
venv/
ENV/

# 环境变量
.env
.env.local
.env.prod
docker-compose.override.yml

# IDE配置
.idea/
.vscode/
.DS_Store

# 日志和缓存
logs/
*.log
.pytest_cache/

# 临时和上传文件
temp_uploads/

# 单元测试覆盖率
htmlcov/
.coverage
coverage.xml
test/
compiled_test/

# Jupyter Notebook检查点
.ipynb_checkpoints/ 

#vue前端项目
VUE/
VUEpkg/
node_modules/
temp_uploads/

#配置文件项目
DeploymentScripts/