from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, Enum, DECIMAL, DateTime
from sqlalchemy.orm import Mapped, mapped_column
from app.core.database import Base
import datetime

class FamilyMember(Base):
    __tablename__ = "family_members"
    
    fuid: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    uid: Mapped[int] = mapped_column(Integer, index=True)
    relationship: Mapped[str] = mapped_column(String(20), index=True)
    name: Mapped[str] = mapped_column(String(50), index=True)
    gender: Mapped[str] = mapped_column(Enum('男', '女'))
    height: Mapped[DECIMAL] = mapped_column(DECIMAL(5, 2), nullable=True)
    weight: Mapped[DECIMAL] = mapped_column(DECIMAL(5, 2), nullable=True)
    birth_year: Mapped[int] = mapped_column(Integer, nullable=True)
    avatar_url: Mapped[str] = mapped_column(String(500), nullable=True)
    create_time: Mapped[datetime.datetime] = mapped_column(DateTime, default=datetime.datetime.utcnow)
    update_time: Mapped[datetime.datetime] = mapped_column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)