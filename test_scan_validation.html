<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康扫描验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .member-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>健康扫描信息完整性验证测试</h1>
    
    <div class="test-section">
        <h2>测试场景</h2>
        <p>测试用户：测试扫描用户 (uid: 31)</p>
        <p>测试目标：验证健康扫描前的信息完整性检查</p>
    </div>

    <div class="test-section">
        <h2>当前用户家庭成员信息</h2>
        <div id="memberInfo" class="member-info">
            <p>正在加载...</p>
        </div>
        <button onclick="loadMemberInfo()">刷新家庭成员信息</button>
    </div>

    <div class="test-section">
        <h2>验证测试</h2>
        <button onclick="testValidation()">测试信息完整性验证</button>
        <div id="validationResult"></div>
    </div>

    <div class="test-section">
        <h2>模拟扫描操作</h2>
        <button onclick="simulateStartCamera()">模拟启动摄像头</button>
        <button onclick="simulateStartRecording()">模拟开始录制</button>
        <div id="scanResult"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        const TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************.ieL6fNj77uWWVScPSgSg4P3EDRb6qVZlWb-m5CK5EZg';
        const USER_ID = 31;

        let familyMembers = [];

        // 加载家庭成员信息
        async function loadMemberInfo() {
            try {
                const response = await fetch(`${API_BASE}/users/${USER_ID}/familylist`, {
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`
                    }
                });
                
                if (response.ok) {
                    familyMembers = await response.json();
                    displayMemberInfo(familyMembers);
                } else {
                    document.getElementById('memberInfo').innerHTML = 
                        `<div class="error">加载失败: ${response.status}</div>`;
                }
            } catch (error) {
                document.getElementById('memberInfo').innerHTML = 
                    `<div class="error">网络错误: ${error.message}</div>`;
            }
        }

        // 显示家庭成员信息
        function displayMemberInfo(members) {
            const container = document.getElementById('memberInfo');
            if (members.length === 0) {
                container.innerHTML = '<div class="warning">没有家庭成员记录</div>';
                return;
            }

            let html = '';
            members.forEach(member => {
                html += `
                    <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 4px;">
                        <h4>${member.name} (${member.relationship})</h4>
                        <p><strong>性别:</strong> ${member.gender || '未设置'}</p>
                        <p><strong>出生年份:</strong> ${member.birth_year || '未设置'}</p>
                        <p><strong>身高:</strong> ${member.height || '未设置'} cm</p>
                        <p><strong>体重:</strong> ${member.weight || '未设置'} kg</p>
                        <p><strong>FUID:</strong> ${member.fuid}</p>
                    </div>
                `;
            });
            container.innerHTML = html;
        }

        // 验证家庭成员基本信息完整性
        function validateMemberBasicInfo(fuid) {
            if (!fuid) {
                return {
                    isValid: false,
                    missingFields: [],
                    memberName: '',
                    error: '未选择家庭成员'
                };
            }

            const member = familyMembers.find(m => m.fuid === fuid);
            if (!member) {
                return {
                    isValid: false,
                    missingFields: [],
                    memberName: '',
                    error: '找不到指定的家庭成员'
                };
            }

            const missingFields = [];

            // 检查性别
            if (!member.gender || member.gender === null || member.gender === '') {
                missingFields.push('性别');
            }

            // 检查出生年份/年龄
            if (!member.birth_year || member.birth_year === null || member.birth_year === 0) {
                missingFields.push('出生年份');
            }

            // 检查身高
            if (!member.height || member.height === null || member.height === 0) {
                missingFields.push('身高');
            }

            // 检查体重
            if (!member.weight || member.weight === null || member.weight === 0) {
                missingFields.push('体重');
            }

            return {
                isValid: missingFields.length === 0,
                missingFields,
                memberName: member.name,
                error: null
            };
        }

        // 测试验证功能
        function testValidation() {
            const container = document.getElementById('validationResult');
            
            if (familyMembers.length === 0) {
                container.innerHTML = '<div class="error">请先加载家庭成员信息</div>';
                return;
            }

            let html = '<h3>验证结果:</h3>';
            
            familyMembers.forEach(member => {
                const result = validateMemberBasicInfo(member.fuid);
                const statusClass = result.isValid ? 'success' : 'error';
                
                html += `
                    <div class="test-result ${statusClass}">
                        <h4>${member.name} (${member.relationship})</h4>
                        <p><strong>验证状态:</strong> ${result.isValid ? '✅ 信息完整' : '❌ 信息不完整'}</p>
                        ${!result.isValid ? `<p><strong>缺少信息:</strong> ${result.missingFields.join('、')}</p>` : ''}
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // 模拟启动摄像头
        function simulateStartCamera() {
            const container = document.getElementById('scanResult');
            
            if (familyMembers.length === 0) {
                container.innerHTML = '<div class="error">请先加载家庭成员信息</div>';
                return;
            }

            // 假设选择第一个家庭成员
            const selectedMember = familyMembers[0];
            const validationResult = validateMemberBasicInfo(selectedMember.fuid);
            
            let html = '<h3>启动摄像头测试结果:</h3>';
            
            if (!validationResult.isValid) {
                const missingFieldsText = validationResult.missingFields.join('、');
                html += `
                    <div class="test-result error">
                        <h4>❌ 启动失败</h4>
                        <p><strong>原因:</strong> ${validationResult.memberName}的基本信息不完整</p>
                        <p><strong>缺少信息:</strong> ${missingFieldsText}</p>
                        <p><strong>建议:</strong> 请前往家庭成员管理页面完善这些信息后再进行健康扫描</p>
                    </div>
                `;
            } else {
                html += `
                    <div class="test-result success">
                        <h4>✅ 启动成功</h4>
                        <p>${validationResult.memberName}的基本信息完整，可以进行健康扫描</p>
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }

        // 模拟开始录制
        function simulateStartRecording() {
            const container = document.getElementById('scanResult');
            
            if (familyMembers.length === 0) {
                container.innerHTML = '<div class="error">请先加载家庭成员信息</div>';
                return;
            }

            // 假设选择第一个家庭成员
            const selectedMember = familyMembers[0];
            const validationResult = validateMemberBasicInfo(selectedMember.fuid);
            
            let html = '<h3>开始录制测试结果:</h3>';
            
            if (!validationResult.isValid) {
                const missingFieldsText = validationResult.missingFields.join('、');
                html += `
                    <div class="test-result error">
                        <h4>❌ 录制失败</h4>
                        <p><strong>原因:</strong> ${validationResult.memberName}的基本信息不完整</p>
                        <p><strong>缺少信息:</strong> ${missingFieldsText}</p>
                        <p><strong>建议:</strong> 请前往家庭成员管理页面完善这些信息后再进行健康扫描</p>
                    </div>
                `;
            } else {
                html += `
                    <div class="test-result success">
                        <h4>✅ 录制开始</h4>
                        <p>${validationResult.memberName}的基本信息完整，可以开始录制健康扫描视频</p>
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }

        // 页面加载时自动加载家庭成员信息
        window.onload = function() {
            loadMemberInfo();
        };
    </script>
</body>
</html>
