from datetime import datetime, timedelta
from typing import Dict, List, Tuple

class RateLimiter:
    def __init__(self, max_attempts: int, window_minutes: int):
        self.max_attempts = max_attempts
        self.window_minutes = window_minutes
        self.attempts: Dict[str, List[datetime]] = {}
    
    def is_allowed(self, identifier: str) -> Tuple[bool, int]:
        """检查是否允许操作，返回(是否允许, 剩余次数)"""
        now = datetime.now()
        window_start = now - timedelta(minutes=self.window_minutes)
        
        # 清理过期记录
        if identifier in self.attempts:
            self.attempts[identifier] = [
                attempt for attempt in self.attempts[identifier] 
                if attempt > window_start
            ]
        else:
            self.attempts[identifier] = []
        
        current_attempts = len(self.attempts[identifier])
        remaining = self.max_attempts - current_attempts
        
        return current_attempts < self.max_attempts, remaining
    
    def record_attempt(self, identifier: str):
        """记录一次操作尝试"""
        if identifier not in self.attempts:
            self.attempts[identifier] = []
        self.attempts[identifier].append(datetime.now())
    
    def reset_attempts(self, identifier: str):
        """重置用户的尝试记录"""
        if identifier in self.attempts:
            del self.attempts[identifier]

# 全局实例
login_rate_limiter = RateLimiter(max_attempts=5, window_minutes=15)
email_rate_limiter = RateLimiter(max_attempts=1, window_minutes=1)  # 1分钟内只能发送1次
