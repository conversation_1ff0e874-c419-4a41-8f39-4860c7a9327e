# FastAPI 用户健康分析系统

一个基于FastAPI的用户健康分析系统，支持用户注册、登录、JWT认证，以及健康视频分析功能。

## 🚀 快速开始

### 方式一：Docker部署（推荐）

#### 1. 环境准备
确保已安装 Docker 和 Docker Compose：
```bash
# 检查Docker版本
docker --version
docker-compose --version
```

#### 2. 配置环境变量

**环境变量优先级说明：**
1. 系统环境变量（export/set/$env:）
2. `.env` 文件（项目根目录，自动被docker-compose和python-dotenv读取）
3. 代码中的默认值

**设置方法：**
- Linux/macOS: `export VAR_NAME=xxx`
- Windows CMD: `set VAR_NAME=xxx`
- Windows PowerShell: `$env:VAR_NAME="xxx"`
- .env 文件: 直接在项目根目录新建 `.env`，每行一个变量，如：
  ```
  MYSQL_HOST=localhost
  MYSQL_USER=root
  ```

**选择数据库模式：**

**方式一：本地Docker MySQL（推荐开发环境）**
```bash
cat > .env << EOF
DB_MODE=local
MYSQL_PASSWORD=your_password
SECRET_KEY=your_secret_key
OSS_ACCESS_KEY_ID=your_key_id
OSS_ACCESS_KEY_SECRET=your_key_secret
OSS_BUCKET=your_bucket
EOF
```

**方式二：阿里云RDS MySQL（推荐生产环境）**
```bash
cat > .env << EOF
DB_MODE=rds
MYSQL_HOST=rm-xxxxxxxxx.mysql.rds.aliyuncs.com
MYSQL_USER=your_rds_username
MYSQL_PASSWORD=your_rds_password
MYSQL_DB=fastapi_users
SECRET_KEY=your_secret_key
OSS_ACCESS_KEY_ID=your_key_id
OSS_ACCESS_KEY_SECRET=your_key_secret
OSS_BUCKET=your_bucket
EOF
```

详细配置说明请参考：[DATABASE_CONFIG.md](DATABASE_CONFIG.md)

#### 3. 启动服务
```bash
# 启动开发环境（本地MySQL）
DB_MODE=local ./start.sh dev

# 启动开发环境（阿里云RDS）
DB_MODE=rds ./start.sh dev

# 启动生产环境（本地MySQL）
DB_MODE=local ./start.sh prod

# 启动生产环境（阿里云RDS）
DB_MODE=rds ./start.sh prod

# 停止所有服务
./start.sh stop

# 重启服务
./start.sh restart

# 查看日志
./start.sh logs

# 查看服务状态
./start.sh status

# 测试数据库连接
./start.sh test-db

# 清理所有数据
./start.sh clean
```

#### 4. 访问服务
- **开发环境**: http://localhost:8000
- **生产环境**: http://test.orange1.cn 或 http://************
- **API文档**: http://localhost:8000/docs (开发环境)

> 生产环境域名 test.orange1.cn 已解析到 ************，后续如有SSL证书可在nginx配置中添加。

### 方式二：本地开发

#### 启动服务
```bash
# 激活虚拟环境
source .venv/bin/activate

# 启动服务
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

## 📋 项目概述

### 主要功能
- ✅ **用户管理** - 注册、登录、JWT认证
- ✅ **安全防护** - 密码加密、防暴力破解
- ✅ **健康分析** - 视频健康状态分析
- ✅ **OSS集成** - 阿里云OSS视频下载（所有环境都需配置）
- ✅ **环境配置** - 灵活的环境变量管理

### 技术栈
- **后端框架**: FastAPI
- **数据库**: MySQL（本地或阿里云RDS）
- **ORM**: SQLAlchemy 2.0
- **认证**: JWT (python-jose)
- **密码加密**: bcrypt (passlib)
- **数据验证**: Pydantic
- **云存储**: 阿里云OSS

## 🔧 环境配置

详见 [DATABASE_CONFIG.md](DATABASE_CONFIG.md)

## 📋 用户接口文档

### 1. 用户注册

**接口地址：** `POST /api/v1/users/register`

**请求参数：**
```json
{
    "name": "用户名",
    "email": "邮箱地址",
    "password": "密码"
}
```

**响应示例：**
```json
{
    "uid": 8,
    "name": "测试用户",
    "email": "<EMAIL>",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**状态码：**
- `201`: 注册成功
- `400`: 邮箱已被注册或参数错误
- `500`: 服务器内部错误

### 2. 用户登录

**接口地址：** `POST /api/v1/users/login`

**请求参数：**
```json
{
    "email": "邮箱地址",
    "password": "密码"
}
```

**响应示例：**
```json
{
    "uid": 8,
    "name": "测试用户",
    "email": "<EMAIL>",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**状态码：**
- `200`: 登录成功
- `401`: 邮箱或密码错误
- `429`: 登录尝试次数过多（防暴力破解）

## 🏥 健康视频分析接口

### 3. 健康视频分析

**接口地址：** `POST /api/v1/health/video`

**请求方式：** `multipart/form-data`

**请求参数：**
- `file` (可选): 视频文件，支持mp4和mov格式，大小限制1MB-100MB
- `video_url` (可选): OSS视频URL，支持阿里云OSS链接
- `name` (必需): 用户姓名
- `gender` (必需): 性别，支持"男"或"女"
- `birth_year` (必需): 出生年份，如1996
- `height` (必需): 身高，单位cm，如181
- `weight` (必需): 体重，单位kg，如100

**请求示例：**
```bash
curl -X 'POST' \
  'http://127.0.0.1:8000/api/v1/health/video' \
  -H 'accept: application/json' \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@4.mp4;type=video/mp4' \
  -F 'video_url=' \
  -F 'name=张五' \
  -F 'gender=男' \
  -F 'birth_year=1996' \
  -F 'height=181' \
  -F 'weight=100'
```

**响应示例：**
```json
{
  "gender": {
    "label": "性别 Gender",
    "value": "男"
  },
  "age": {
    "label": "年龄 Age",
    "value": 29,
    "unit": "岁 years"
  },
  "height": {
    "label": "身高 Height",
    "value": 181,
    "unit": "cm"
  },
  "weight": {
    "label": "体重 Weight",
    "value": 100,
    "unit": "kg"
  },
  "bmi": {
    "label": "体质指数 BMI",
    "value": 30.52,
    "unit": "kg/m²"
  },
  "heart_rate": {
    "label": "心率 Heart Rate",
    "value": 78.31,
    "unit": "次/分 BPM"
  },
  "pulse_rate": {
    "label": "脉搏率 Pulse Rate",
    "value": 83.06,
    "unit": "次/分 BPM"
  },
  "hrv": {
    "label": "心率变异性 HRV",
    "time_domain": {
      "label": "时域指标 Time Domain",
      "value": {
        "ibi": 722.40,
        "sdnn": 92.21,
        "rmssd": 76.05,
        "pnn20": 0.83,
        "pnn50": 0.58,
        "hr_mad": 54.83
      }
    },
    "frequency_domain": {
      "label": "频域指标 Frequency Domain",
      "value": {
        "VLF": 2.04,
        "TP": 60.70,
        "LF": 17.94,
        "HF": 40.71,
        "LF/HF": 0.44
      }
    },
    "nonlinear": {
      "label": "非线性指标 Nonlinear",
      "value": {
        "sd1": 53.77,
        "sd2": 70.21,
        "s": 11859.79,
        "sd1/sd2": 0.77
      }
    }
  },
  "blood_pressure": {
    "label": "血压 Blood Pressure",
    "value": {
      "SBP": 148,
      "DBP": 96.2
    },
    "unit": "mmHg"
  },
  "spo2": {
    "label": "血氧饱和度 SpO2",
    "value": 97.5,
    "unit": "%"
  },
  "breathing_rate": {
    "label": "呼吸频率 Breathing Rate",
    "value": 18,
    "unit": "次/分 rpm"
  },
  "cardiac_risk": {
    "label": "心脏风险 Cardiac Risk",
    "value": "低"
  },
  "brain_risk": {
    "label": "脑风险 Brain Risk",
    "value": "低"
  },
  "afib": {
    "label": "房颤风险 AFib Risk",
    "value": "低"
  },
  "arrhythmia": {
    "label": "心率不齐风险 Arrhythmia Risk",
    "value": "低"
  },
  "anemia": {
    "label": "是否贫血 Anemia",
    "value": "否"
  },
  "hemoglobin": {
    "label": "血红蛋白 Hemoglobin",
    "value": 145,
    "unit": "g/L"
  },
  "risk_assessment": {
    "label": "风险评估 Risk Assessment",
    "value": "低风险"
  },
  "signal_quality": {
    "label": "信号质量 Signal Quality",
    "value": 0.58,
    "unit": "0~1"
  },
  "bvp_waveform": {
    "label": "BVP波形 BVP Waveform",
    "value": {
      "bvp": [1.08, 0.66, 0.28, ...],
      "timestamps": [0, 0.033, 0.067, ...],
      "sampling_rate": 30
    },
    "unit": "标准化"
  }
}
```

**状态码：**
- `200`: 分析成功
- `400`: 请求参数错误或文件格式不支持
- `413`: 文件过大
- `422`: 文件格式错误
- `500`: 服务器内部错误

**健康指标说明：**

| 指标 | 说明 | 单位 |
|------|------|------|
| 心率 (Heart Rate) | 每分钟心跳次数 | 次/分 (BPM) |
| 脉搏率 (Pulse Rate) | 每分钟脉搏次数 | 次/分 (BPM) |
| 血压 (Blood Pressure) | 收缩压/舒张压 | mmHg |
| 血氧饱和度 (SpO2) | 血液中氧气的饱和度 | % |
| 呼吸频率 (Breathing Rate) | 每分钟呼吸次数 | 次/分 (rpm) |
| BMI | 体质指数，体重(kg)/身高(m)² | kg/m² |
| 心率变异性 (HRV) | 心跳间隔的变化程度 | 多种指标 |
| 信号质量 (Signal Quality) | 视频信号质量评分 | 0~1 |

**风险评估说明：**
- **心脏风险**: 低/中/高 - 基于心率、血压等指标评估
- **脑风险**: 低/中/高 - 基于血压、血氧等指标评估
- **房颤风险**: 低/中/高 - 基于心率变异性评估
- **心率不齐风险**: 低/中/高 - 基于心率规律性评估
- **贫血**: 是/否 - 基于血红蛋白水平评估

## 🔐 如何添加需要Token认证的接口

### 1. 在用户接口文件中添加新接口

在 `app/api/v1/user.py` 中添加需要认证的接口：

```python
from app.core.auth import get_current_user  # 添加这个导入

# 获取当前用户信息（需要token认证）
@router.get("/me", response_model=UserResponse)
def get_current_user_info(current_user: dict = Depends(get_current_user), db: Session = Depends(get_db)):
    """获取当前用户信息 - 需要token认证"""
    user = crud_user.get_user_by_uid(db, int(current_user["sub"]))
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return UserResponse(
        uid=user.uid,
        name=user.name,
        email=user.email,
        token=user.token
    )
```

### 2. 调用需要认证的接口

```python
import requests

# 1. 先登录获取token
login_data = {
    "email": "<EMAIL>",
    "password": "password123"
}
response = requests.post("http://localhost:8000/api/v1/users/login", json=login_data)
token = response.json()["token"]

# 2. 使用token调用需要认证的接口
headers = {"Authorization": f"Bearer {token}"}
response = requests.get("http://localhost:8000/api/v1/users/me", headers=headers)
print(response.json())
```

## 🛡️ 安全特性

### 1. 密码加密
- 所有密码使用bcrypt算法进行哈希加密
- 数据库中不存储明文密码
- 密码验证使用安全的哈希比较

### 2. Token认证
- 登录成功后返回JWT token
- Token有效期为24小时（1440分钟）
- 使用HS256算法签名
- 后续请求需要在Header中携带token：
  ```
  Authorization: Bearer <token>
  ```

### 3. 防暴力破解
- 15分钟内最多允许5次登录尝试
- 超过限制后需要等待15分钟才能继续尝试
- 登录成功后重置尝试记录
- 基于邮箱地址进行限制，不同邮箱独立计算

### 4. 数据验证
- 邮箱格式验证
- 邮箱唯一性检查
- 参数完整性验证

## 🗄️ 数据库表结构

```sql
CREATE TABLE `users` (
  `uid` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `token` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`uid`) USING BTREE,
  UNIQUE KEY `ix_users_email` (`email`),
  KEY `ix_users_name` (`name`),
  KEY `ix_users_id` (`uid`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
```

## 📹 OSS视频下载问题排查

### 常见错误及解决方案

#### 1. OSS配置不完整错误
```
ERROR:OSS视频下载失败: OSS配置不完整，请检查环境变量
```

**解决方案：**
确保设置以下环境变量：
```bash
export OSS_ACCESS_KEY_ID=your_access_key_id
export OSS_ACCESS_KEY_SECRET=your_access_key_secret
export OSS_REGION=cn-beijing
export OSS_BUCKET=your_bucket_name
export OSS_ENDPOINT=oss-cn-beijing.aliyuncs.com
```

#### 2. Bucket名称无效错误
```
ERROR:OSS视频下载失败: OSS下载失败: operation error GetObject: Bucket name is invalid
```

**解决方案：**
已修复URL解析逻辑，现在支持多种OSS URL格式：
- `https://bucket.endpoint/object_key`
- `https://endpoint/bucket/object_key`
- `https://bucket.oss-region.aliyuncs.com/object_key`

#### 3. StreamBodyReader不可迭代错误
```
ERROR:OSS视频下载失败: OSS下载失败: 'StreamBodyReader' object is not iterable
```

**解决方案：**
已改用requests库进行OSS下载，更简单可靠。

### 支持的OSS URL格式

1. **标准格式**：`https://bucket.endpoint/object_key`
   - 示例：`https://saas-one.oss-cn-beijing.aliyuncs.com/video/test.mp4`

2. **路径格式**：`https://endpoint/bucket/object_key`
   - 示例：`https://oss-cn-beijing.aliyuncs.com/saas-one/video/test.mp4`

3. **简化格式**：`https://bucket.oss-region.aliyuncs.com/object_key`
   - 示例：`https://saas-one.oss-cn-beijing.aliyuncs.com/test.mp4`

### 使用方法

```bash
# 测试OSS配置
python test_oss_download.py

# 使用API
curl -X POST "http://localhost:8000/health/video" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "video_url=https://your-bucket.oss-cn-beijing.aliyuncs.com/video/test.mp4"
```

## 🧪 测试

### 运行用户接口测试
```bash
python test_user_api.py
```

### 运行OSS下载测试
```bash
python test_oss_download.py
```

### 测试结果
✅ **用户注册功能** - 成功创建用户，返回用户信息和JWT token  
✅ **用户登录功能** - 成功验证用户，返回用户信息和新的JWT token  
✅ **防暴力破解保护** - 错误密码登录被正确拒绝，剩余尝试次数递减  
✅ **重复注册保护** - 相同邮箱重复注册被正确拒绝  
✅ **Token认证** - JWT token生成和验证正常  

## 📝 使用示例

### Python示例

```python
import requests

# 注册用户
register_data = {
    "name": "测试用户",
    "email": "<EMAIL>",
    "password": "password123"
}

response = requests.post("http://localhost:8000/api/v1/users/register", json=register_data)
if response.status_code == 201:
    user_data = response.json()
    token = user_data["token"]
    print(f"注册成功，用户ID: {user_data['uid']}")

# 登录用户
login_data = {
    "email": "<EMAIL>",
    "password": "password123"
}

response = requests.post("http://localhost:8000/api/v1/users/login", json=login_data)
if response.status_code == 200:
    user_data = response.json()
    token = user_data["token"]
    print(f"登录成功，用户ID: {user_data['uid']}")

# 使用token访问需要认证的接口
headers = {"Authorization": f"Bearer {token}"}
response = requests.get("http://localhost:8000/api/v1/users/me", headers=headers)
if response.status_code == 200:
    print(f"用户信息: {response.json()}")

# 健康视频分析
files = {
    'file': ('video.mp4', open('video.mp4', 'rb'), 'video/mp4')
}
data = {
    'name': '张三',
    'gender': '男',
    'birth_year': '1990',
    'height': '175',
    'weight': '70'
}

response = requests.post("http://localhost:8000/api/v1/health/video", files=files, data=data)
if response.status_code == 200:
    health_data = response.json()
    print(f"心率: {health_data['heart_rate']['value']} {health_data['heart_rate']['unit']}")
    print(f"血压: {health_data['blood_pressure']['value']['SBP']}/{health_data['blood_pressure']['value']['DBP']} {health_data['blood_pressure']['unit']}")
    print(f"风险评估: {health_data['risk_assessment']['value']}")
```

### cURL示例

```bash
# 注册用户
curl -X POST "http://localhost:8000/api/v1/users/register" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试用户",
    "email": "<EMAIL>",
    "password": "password123"
  }'

# 登录用户
curl -X POST "http://localhost:8000/api/v1/users/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'

# 使用token访问需要认证的接口
curl -X GET "http://localhost:8000/api/v1/users/me" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"

# 健康视频分析
curl -X POST "http://localhost:8000/api/v1/health/video" \
  -H "accept: application/json" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@video.mp4;type=video/mp4" \
  -F "name=张三" \
  -F "gender=男" \
  -F "birth_year=1990" \
  -F "height=175" \
  -F "weight=70"
```

## ⚠️ 错误处理

### 常见错误码

- `400 Bad Request`: 请求参数错误或邮箱已被注册
- `401 Unauthorized`: 认证失败（邮箱或密码错误，或token无效）
- `404 Not Found`: 用户不存在
- `413 Payload Too Large`: 文件过大（健康视频分析）
- `422 Unprocessable Entity`: 文件格式错误（健康视频分析）
- `429 Too Many Requests`: 登录尝试次数过多
- `500 Internal Server Error`: 服务器内部错误

### 错误响应格式

```json
{
    "detail": "错误描述信息"
}
```

### 防暴力破解错误示例

```json
{
    "detail": "邮箱或密码错误，剩余尝试次数: 4"
}
```

```json
{
    "detail": "登录尝试次数过多，请15分钟后再试"
}
```

### Token认证错误示例

```json
{
    "detail": "无效的认证凭据"
}
```

## 📋 注意事项

1. 邮箱地址必须是有效的邮箱格式
2. 密码建议使用强密码（包含大小写字母、数字和特殊字符）
3. Token过期后需要重新登录获取新token
4. 防暴力破解机制基于邮箱地址，不同邮箱地址的尝试次数独立计算
5. 在生产环境中，请修改`SECRET_KEY`为安全的随机字符串
6. 确保数据库表结构与上述SQL语句一致
7. 所有密码都会自动加密存储，无需手动处理
8. 需要认证的接口必须在Header中携带有效的token
9. Token格式：`Authorization: Bearer <your_token>`
10. 确保OSS bucket有正确的访问权限
11. 健康视频分析支持mp4和mov格式，文件大小限制1MB-100MB
12. 健康视频分析需要提供用户基本信息（姓名、性别、出生年份、身高、体重）
13. 视频文件会自动清理，不会占用磁盘空间
14. 健康分析结果包含心率、血压、血氧饱和度等多项生理指标
15. 系统会自动评估心脏风险、脑风险、房颤风险等健康风险

## 🔍 故障排除

### 用户接口问题
1. 检查数据库连接是否正常
2. 确认环境变量配置正确
3. 查看应用日志：`tail -f logs/app.log`

### OSS下载问题
1. **网络连接**：确保服务器能访问阿里云OSS
2. **权限配置**：检查AccessKey是否有下载权限
3. **Bucket配置**：确认bucket名称和region正确
4. **文件存在性**：确认OSS中的文件确实存在

### 健康视频分析问题
1. **文件格式**：确保视频文件为mp4或mov格式
2. **文件大小**：检查文件大小是否在1MB-100MB范围内
3. **视频质量**：确保视频清晰度足够，人脸可见
4. **光照条件**：避免过暗或过亮的环境
5. **拍摄角度**：确保人脸正面朝向摄像头
6. **系统资源**：检查服务器CPU和内存是否充足

## 🐳 Docker管理

### 常用命令

#### 使用启动脚本（推荐）
```bash
# 启动开发环境（本地MySQL）
DB_MODE=local ./start.sh dev

# 启动开发环境（阿里云RDS）
DB_MODE=rds ./start.sh dev

# 启动生产环境（本地MySQL）
DB_MODE=local ./start.sh prod

# 启动生产环境（阿里云RDS）
DB_MODE=rds ./start.sh prod

# 停止所有服务
./start.sh stop

# 重启服务
./start.sh restart

# 查看日志
./start.sh logs

# 查看服务状态
./start.sh status

# 测试数据库连接
./start.sh test-db

# 清理所有数据
./start.sh clean
```

#### 使用Docker Compose
```bash
# 本地MySQL开发环境
docker-compose -f docker-compose.yml -f docker-compose.override.yml -f docker-compose.mysql.yml up -d

# 阿里云RDS开发环境
docker-compose -f docker-compose.yml -f docker-compose.override.yml -f docker-compose.rds.yml up -d

# 本地MySQL生产环境
docker-compose -f docker-compose.yml -f docker-compose.prod.yml -f docker-compose.mysql.yml up -d

# 阿里云RDS生产环境
docker-compose -f docker-compose.yml -f docker-compose.prod.yml -f docker-compose.rds.yml up -d

# 停止服务
docker-compose down

# 查看日志
docker-compose logs -f web

# 重建镜像
docker-compose build --no-cache

# 测试数据库连接
docker-compose -f docker-compose.yml -f docker-compose.rds.yml --profile test up db-test
```

### 环境组合推荐
- 本地开发 + 本地MySQL：适合个人开发、调试
- 本地开发 + 阿里云RDS：适合联调、测试
- 生产环境 + 阿里云RDS：线上正式环境
- OSS：所有环境都需配置

## 📚 API文档

### 文档访问
- **开发环境**：访问 `http://localhost:8000/docs` 查看Swagger UI文档
- **生产环境**：设置 `ENABLE_DOCS=false` 禁用文档页面

### 文档控制
通过环境变量 `ENABLE_DOCS` 控制API文档的显示：

```bash
# 启用文档（开发环境推荐）
ENABLE_DOCS=true

# 禁用文档（生产环境推荐）
ENABLE_DOCS=false
```

**配置方式：**
1. 在 `.env` 文件中设置：`ENABLE_DOCS=false`
2. 设置系统环境变量：`export ENABLE_DOCS=false`
3. Docker环境：在 `docker-compose.yml` 中设置 `ENABLE_DOCS=false`

**效果：**
- `ENABLE_DOCS=true`：可以访问 `/docs` 和 `/redoc` 页面
- `ENABLE_DOCS=false`：`/docs` 和 `/redoc` 页面返回404错误

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## �� 许可证

本项目采用MIT许可证。
