from pydantic import BaseModel
from typing import Optional
from decimal import Decimal

class FamilyMemberBase(BaseModel):
    relationship: str
    name: str
    gender: Optional[str] = None
    height: Optional[Decimal] = None
    weight: Optional[Decimal] = None
    birth_year: Optional[int] = None
    avatar_url: Optional[str] = None

class FamilyMemberCreate(FamilyMemberBase):
    uid: int

class FamilyMemberResponse(FamilyMemberBase):
    fuid: int
    uid: int
    
    class Config:
        from_attributes = True