from fastapi import APIRouter, HTTPException, status, Request
from sqlalchemy.orm import Session
from app.core.database import get_db_session, close_db_session
from app.core.auth import extract_token_from_request, get_current_user
from app.crud import family_member as crud_family_member
from app.crud import health_data as crud_health_data
from typing import Optional, Dict, Any
import datetime

router = APIRouter()

@router.get("/{uid}")
@router.get("/{uid}/{fuid}")
async def get_home_data(
    uid: int,
    request: Request,
    fuid: Optional[int] = None
) -> Dict[str, Any]:
    """获取首页数据接口 - 需要token认证"""
    # 验证token并获取当前用户信息
    token = extract_token_from_request(request)
    current_user = get_current_user(token)
    
    db = get_db_session()
    try:
        # 验证用户权限
        current_user_id = int(current_user.get("sub", 0))
        if uid != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问其他用户的数据"
            )

        # 初始化默认返回数据（只包含实际存在的字段）
        family_member_data = {
            "fuid": 0,
            "uid": uid,
            "relationship": "",
            "name": "",
            "gender": "",
            "height": 0.0,
            "weight": 0.0,
            "birth_year": 0,
            "age": 0,  # 计算得出的年龄
            "avatar_url": ""
        }
        
        health_report_data = None
        bvp_waveform_data = None

        # 如果指定了fuid，获取家庭成员信息
        if fuid is not None:
            family_member = crud_family_member.get_family_member_by_fuid(db, fuid)
            if family_member and family_member.uid == uid:
                # 计算年龄
                current_year = datetime.datetime.now().year
                age = current_year - family_member.birth_year if family_member.birth_year else 0
                
                family_member_data = {
                    "fuid": family_member.fuid,
                    "uid": family_member.uid,
                    "relationship": family_member.relationship or "",
                    "name": family_member.name or "",
                    "gender": family_member.gender or "",
                    "height": family_member.height or 0.0,
                    "weight": family_member.weight or 0.0,
                    "birth_year": family_member.birth_year or 0,
                    "age": age,
                    "avatar_url": family_member.avatar_url or ""
                }
                
                # 获取该家庭成员的最新健康数据
                health_report_data = crud_health_data.get_latest_health_report_by_fuid(db, uid, fuid)
                if health_report_data:
                    bvp_waveform_data = crud_health_data.get_bvp_waveform_by_report_id(db, health_report_data.report_id)
        else:
            # fuid为None，获取用户本人的最新健康数据
            health_report_data = crud_health_data.get_latest_health_report_by_uid(db, uid)
            if health_report_data:
                bvp_waveform_data = crud_health_data.get_bvp_waveform_by_report_id(db, health_report_data.report_id)
        
        # 序列化健康报告数据
        health_report_serialized = None
        if health_report_data:
            health_report_serialized = {
                "id": health_report_data.id,
                "report_id": health_report_data.report_id,
                "uid": health_report_data.uid,
                "fuid": health_report_data.fuid,
                "name": health_report_data.name,
                "gender": health_report_data.gender,
                "age": health_report_data.age,
                "height": health_report_data.height,
                "weight": health_report_data.weight,
                "bmi": health_report_data.bmi,
                "heart_rate": health_report_data.heart_rate,
                "pulse_rate": health_report_data.pulse_rate,
                "blood_pressure": health_report_data.blood_pressure,
                # 为了兼容性，同时提供分离的血压字段
                "systolic_pressure": health_report_data.blood_pressure.get('SBP') if health_report_data.blood_pressure else None,
                "diastolic_pressure": health_report_data.blood_pressure.get('DBP') if health_report_data.blood_pressure else None,
                "spo2": health_report_data.spo2,
                "breathing_rate": health_report_data.breathing_rate,
                "cardiac_risk": health_report_data.cardiac_risk,
                "brain_risk": health_report_data.brain_risk,
                "afib": health_report_data.afib,
                "arrhythmia": health_report_data.arrhythmia,
                "anemia": health_report_data.anemia,
                "hemoglobin": health_report_data.hemoglobin,
                "risk_assessment": health_report_data.risk_assessment,
                "signal_quality": health_report_data.signal_quality,
                "hrv_time_domain": health_report_data.hrv_time_domain,
                "hrv_frequency_domain": health_report_data.hrv_frequency_domain,
                "hrv_nonlinear": health_report_data.hrv_nonlinear,
                "hrv": health_report_data.hrv,
                "extra": health_report_data.extra,
                "create_time": health_report_data.create_time.isoformat() if health_report_data.create_time else None,
                "update_time": health_report_data.update_time.isoformat() if health_report_data.update_time else None
            }

        # 序列化BVP波形数据
        bvp_waveform_serialized = None
        if bvp_waveform_data:
            bvp_waveform_serialized = {
                "id": bvp_waveform_data.id,
                "report_id": bvp_waveform_data.report_id,
                "bvp": bvp_waveform_data.bvp,
                "timestamps": bvp_waveform_data.timestamps,
                "sampling_rate": bvp_waveform_data.sampling_rate,
                "create_time": bvp_waveform_data.create_time.isoformat() if bvp_waveform_data.create_time else None,
                "update_time": bvp_waveform_data.update_time.isoformat() if bvp_waveform_data.update_time else None
            }

        return {
            "family_member": family_member_data,
            "health_report": health_report_serialized,
            "bvp_waveform": bvp_waveform_serialized,
            "metadata": {
                "uid": uid,
                "fuid": fuid,
                "has_health_data": health_report_data is not None,
                "has_bvp_data": bvp_waveform_data is not None,
                "is_family_member": fuid is not None,
                "query_time": datetime.datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取首页数据失败: {str(e)}"
        )
    finally:
        close_db_session(db)


@router.get("/historyreports/{uid}")
async def get_user_history_reports(
    uid: int,
    request: Request,
    limit: Optional[int] = 10,
    offset: Optional[int] = 0
) -> Dict[str, Any]:
    """获取用户历史健康报告接口"""
    # 验证token并获取当前用户信息
    token = extract_token_from_request(request)
    current_user = get_current_user(token)

    db = get_db_session()
    try:
        # 验证用户权限
        current_user_id = int(current_user.get("sub", 0))
        if uid != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限访问其他用户的数据"
            )

        # 获取用户的"本人"家庭成员记录
        self_member = crud_family_member.get_family_member_by_relationship(
            db, uid=uid, relationship="本人"
        )

        if not self_member:
            return {
                "reports": [],
                "total": 0,
                "message": "请先添加个人信息"
            }

        # 获取该成员的所有健康报告
        reports = crud_health_data.get_health_reports_by_fuid(
            db, fuid=self_member.fuid, limit=limit, offset=offset
        )

        # 获取总数
        total_count = crud_health_data.get_health_reports_count_by_fuid(
            db, fuid=self_member.fuid
        )

        # 格式化报告数据
        formatted_reports = []
        for report in reports:
            formatted_report = {
                "report_id": report.id,
                "create_time": report.create_time.isoformat() if report.create_time else None,
                "member_name": self_member.name,
                "member_fuid": self_member.fuid,
                "heart_rate": report.heart_rate,
                "blood_pressure": report.blood_pressure,
                "spo2": report.spo2,
                "breathing_rate": report.breathing_rate,
                "cardiac_risk": report.cardiac_risk,
                "brain_risk": report.brain_risk,
                "afib": report.afib,
                "signal_quality": report.signal_quality
            }
            formatted_reports.append(formatted_report)

        return {
            "reports": formatted_reports,
            "total": total_count,
            "member_info": {
                "fuid": self_member.fuid,
                "name": self_member.name,
                "relationship": self_member.relationship
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取历史报告失败: {str(e)}"
        )
    finally:
        close_db_session(db)


@router.get("/familyhistoryreports/{uid}/{fuid}")
async def get_family_member_history_reports(
    uid: int,
    fuid: int,
    request: Request,
    limit: Optional[int] = 10,
    offset: Optional[int] = 0
) -> Dict[str, Any]:
    """获取指定家庭成员的历史健康报告接口"""
    # 验证token并获取当前用户信息
    token = extract_token_from_request(request)
    current_user = get_current_user(token)

    db = get_db_session()
    try:
        # 验证用户权限
        current_user_id = int(current_user.get("sub", 0))
        if uid != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限访问其他用户的数据"
            )

        # 验证家庭成员是否属于该用户
        family_member = crud_family_member.get_family_member_by_fuid(db, fuid=fuid)
        if not family_member or family_member.uid != uid:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="家庭成员不存在或无权限访问"
            )

        # 获取该成员的所有健康报告
        reports = crud_health_data.get_health_reports_by_fuid(
            db, fuid=fuid, limit=limit, offset=offset
        )

        # 获取总数
        total_count = crud_health_data.get_health_reports_count_by_fuid(
            db, fuid=fuid
        )

        # 格式化报告数据
        formatted_reports = []
        for report in reports:
            formatted_report = {
                "report_id": report.id,
                "create_time": report.create_time.isoformat() if report.create_time else None,
                "member_name": family_member.name,
                "member_fuid": family_member.fuid,
                "heart_rate": report.heart_rate,
                "blood_pressure": report.blood_pressure,
                "spo2": report.spo2,
                "breathing_rate": report.breathing_rate,
                "cardiac_risk": report.cardiac_risk,
                "brain_risk": report.brain_risk,
                "afib": report.afib,
                "signal_quality": report.signal_quality
            }
            formatted_reports.append(formatted_report)

        return {
            "reports": formatted_reports,
            "total": total_count,
            "member_info": {
                "fuid": family_member.fuid,
                "name": family_member.name,
                "relationship": family_member.relationship
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取家庭成员历史报告失败: {str(e)}"
        )
    finally:
        close_db_session(db)
