import os
import glob
from setuptools import setup, find_packages
from Cython.Build import cythonize
from Cython.Distutils import build_ext

# 修复Cython 3.x兼容性的编译器指令 - 启用安全检查防止Segmentation fault
compiler_directives = {
    'language_level': 3,
    'boundscheck': True,      # 启用边界检查，防止内存越界
    'wraparound': True,       # 启用负索引检查
    'initializedcheck': True, # 启用初始化检查
    'cdivision': False,       # 禁用C风格除法，使用Python除法
    'embedsignature': True,
    'binding': True,
    # 添加兼容性指令
    'legacy_implicit_noexcept': True,  # 兼容旧版本
    # 添加生成器相关的兼容性设置
    'c_string_type': 'unicode',
    'c_string_encoding': 'utf8',
    # 添加安全性指令
    'overflowcheck': True,    # 启用溢出检查
}

# 编译器选项 - 使用更安全的优化级别
extra_compile_args = [
    '-O2',                    # 降低优化级别，避免过度优化导致的问题
    # '-ffast-math',          # 禁用快速数学，可能导致数值不稳定
    # '-march=native',        # 禁用本地架构优化，提高兼容性
    # 添加兼容性和调试标志
    '-Wno-deprecated-declarations',
    '-Wno-unreachable-code',
    '-g',                     # 添加调试信息，便于定位问题
]

# 需要排除的文件（保持为.py格式）- 排除容易导致Segmentation fault的模块
EXCLUDE_FILES = [
    'app/main.py',
    'app/__init__.py',
    'app/*/__init__.py',
    'app/*/*/__init__.py',
    'app/*/*/*/__init__.py',
    # 排除包含第三方C扩展的模块，避免与Cython冲突
    'app/utils/rppg_tool.py',      # 包含rppg C扩展，可能导致内存冲突
    'app/utils/health_analyzer.py', # 复杂的NumPy操作，可能导致内存问题
    # 排除视频分析相关的核心模块，保持Python源码
    'app/api/v1/health.py',        # 视频分析API入口，包含文件操作
    # 如果@contextmanager还有问题，可以排除数据库文件
    # 'app/core/database.py',
]

# 需要排除的目录
EXCLUDE_DIRS = [
    'app/migrations',
    'app/tests',
    'app/test_*',
]

def should_exclude_file(file_path):
    """检查文件是否应该被排除"""
    import fnmatch
    
    # 转换为相对路径
    rel_path = os.path.relpath(file_path)
    
    # 检查文件排除规则
    for pattern in EXCLUDE_FILES:
        if fnmatch.fnmatch(rel_path, pattern):
            return True
    
    # 检查目录排除规则
    for exclude_dir in EXCLUDE_DIRS:
        if exclude_dir in rel_path:
            return True
    
    return False

def find_python_files():
    """查找需要编译的Python文件"""
    python_files = []
    
    for root, dirs, files in os.walk('app'):
        # 跳过排除的目录
        dirs[:] = [d for d in dirs if not any(exclude in os.path.join(root, d) for exclude in EXCLUDE_DIRS)]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                if not should_exclude_file(file_path):
                    python_files.append(file_path)
    
    return python_files

# 获取需要编译的Python文件
python_files = find_python_files()

print(f"找到 {len(python_files)} 个Python文件需要编译")
for py_file in python_files[:5]:  # 只显示前5个
    print(f"  - {py_file}")
if len(python_files) > 5:
    print(f"  ... 还有 {len(python_files) - 5} 个文件")

# 自定义构建类
class CustomBuildExt(build_ext):
    def run(self):
        try:
            # 执行标准构建
            build_ext.run(self)
            print("✅ Cython编译完成")
        except Exception as e:
            print(f"❌ Cython编译失败: {e}")
            # 不退出，继续执行
        
        # 检查是否需要删除原始.py文件
        if os.environ.get('CYTHON_DELETE_PY_FILES', '').lower() == 'true':
            self.delete_original_py_files()
    
    def delete_original_py_files(self):
        """删除已编译的原始.py文件"""
        print("\n🗑️  删除已编译的原始.py文件...")
        
        deleted_count = 0
        for py_file in python_files:
            if os.path.exists(py_file):
                # 检查对应的.so文件是否存在
                so_pattern = py_file.replace('.py', '.cpython-*-*.so')
                so_files = glob.glob(so_pattern)
                
                if so_files:
                    try:
                        os.remove(py_file)
                        print(f"  ✅ 删除: {py_file}")
                        deleted_count += 1
                    except OSError as e:
                        print(f"  ❌ 删除失败: {py_file} - {e}")
                else:
                    print(f"  ⚠️  跳过: {py_file} (未找到对应的.so文件)")
        
        print(f"\n✅ 共删除 {deleted_count} 个原始.py文件")

# 安全的Cython配置
try:
    ext_modules = cythonize(
        python_files,
        compiler_directives=compiler_directives,
        build_dir="build",
        language_level=3,
        # 添加错误处理
        force=True,  # 强制重新编译
        quiet=False,  # 显示详细信息
    )
    
    # 为每个扩展模块添加编译选项
    for ext in ext_modules:
        ext.extra_compile_args = extra_compile_args
        
except Exception as e:
    print(f"❌ Cython配置失败: {e}")
    print("使用空的扩展模块列表")
    ext_modules = []

setup(
    name="fastapi-health-system",
    version="1.0.0",
    packages=find_packages(),
    ext_modules=ext_modules,
    cmdclass={'build_ext': CustomBuildExt},
    zip_safe=False,
    python_requires='>=3.9',
    install_requires=[
        # 运行时依赖
    ],
)
